"use client"

import { useState, useEffect, useRef, use<PERSON><PERSON>back, useMemo } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { MessageSquare, FileText, Mic, Loader, Info, Book, X, ChevronLeft, ChevronRight, MessageCircle, Video } from "lucide-react"
import { CompactThemeToggle } from "../ThemeToggle"
import ChatTab from "./ChatTab"
import { getFirestore, collection, query, where, getDocs, addDoc, serverTimestamp, orderBy, limit, doc, getDoc } from "firebase/firestore"
import { useGetNamespace } from "./useGetNamespace"
import { useSession } from "next-auth/react"
import { v4 as uuidv4 } from "uuid"
import useUpload, { StatusText } from "./useUpload"
import { useConversation } from "@elevenlabs/react"
import { db } from "components/firebase"
import SideBar from "./SideBar"
import Rehearsals from "./Rehearsals"
import FileDetails from "./FileDetails"
import ScriptTab from "./ScriptTab"
import ResponseTab from "./ResponseTab"
import { updateAgentVoice, getAgentConfiguration, extractAgentName, createElevenLabsClient, configureAgentClientTools } from "./elevenlabs"
import { useAgentModality, type AgentModalityType } from "./useAgentModality"
import { AVAILABLE_VOICES } from "./voiceUtils"

interface ScriptFile {
  id: string
  name: string
  namespace: string
}

interface ChatMessage {
  id?: string
  tempId?: string
  role: "user" | "assistant"
  content: string
  timestamp: string
  audioUrl?: string
  fileDocumentId?: string
}

interface ReadermodalProps {
  isOpen: boolean
  onClose: () => void
  fileId?: string
}

function Readermodal({ isOpen, onClose, fileId }: ReadermodalProps) {
  const [activeTab, setActiveTab] = useState<string | null>(null)
  const [activeSection, setActiveSection] = useState<string>("rehearsing")
  const [scriptFiles, setScriptFiles] = useState<ScriptFile[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isUploading, setIsUploading] = useState<boolean>(false)
  const [uploadProgress, setUploadProgress] = useState<number | null>(null)
  const [uploadStatusText, setUploadStatusText] = useState<string>("Uploading script...")
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [chatId, setChatId] = useState<string | null>(null)
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([])
  const [fileDocumentId, setFileDocumentId] = useState<string | null>(null)
  const [selectedFileNamespace, setSelectedFileNamespace] = useState<string | null>(null)
  const [fileName, setFileName] = useState<string | null>(null)
  const isMounted = useRef(true)
  const messagesProcessingRef = useRef(false)
  const [hasPermission, setHasPermission] = useState(false)
  const [isMuted, setIsMuted] = useState(false)
  const [isListening, setIsListening] = useState(false)
  const [voiceErrorMessage, setVoiceErrorMessage] = useState("")
  const [apiConfigStatus, setApiConfigStatus] = useState<'unchecked' | 'valid' | 'invalid' | 'connecting'>('unchecked')
  const [detailedErrorInfo, setDetailedErrorInfo] = useState<string | null>(null)
  const { data: session, status: sessionStatus } = useSession()
  const userId = session?.user?.email || ""
  const { handleUpload, progress, status, error: uploadError } = useUpload()
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false)

  // Script-related state variables
  const [scriptContent, setScriptContent] = useState<string>("")
  const [isScriptLoading, setIsScriptLoading] = useState<boolean>(false)
  const [isScriptReady, setIsScriptReady] = useState<boolean>(false)
  const [isFormatting, setIsFormatting] = useState<boolean>(false)
  const [formattedMarkdown, setFormattedMarkdown] = useState<string>("")

  // Voice selection state
  const [selectedVoiceId, setSelectedVoiceId] = useState<string | null>(null)
  const [isUpdatingVoice, setIsUpdatingVoice] = useState(false)

  // Auto-trigger functionality removed - users must manually navigate to script tab

  // Agent modality state
  const { agentModality, setAgentModality, generatePrompt, updateAgentPrompt } = useAgentModality()

  // Agent name state
  const [agentName, setAgentName] = useState<string | null>(null)

  // Delayed script tab switching removed - manual navigation required

  // Force tool registration when component mounts or modality changes
  useEffect(() => {
    const ensureToolRegistration = async () => {
      if (!isOpen) return

      try {
        const agentId = process.env.NEXT_PUBLIC_ELEVENLABS_SCRIPT_AGENT_ID || '1WU4LPk9482VXQFb80aq'
        const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY || '***************************************************'

        console.log('[TOOL_REGISTRATION] 🔧 Ensuring function tool is registered...')

        // Step 1: Configure client tools first
        console.log('[TOOL_REGISTRATION] Step 1: Configuring client tools...')
        await configureAgentClientTools(agentId, apiKey)

        // Step 2: Update the agent prompt which will also register any additional tools
        console.log('[TOOL_REGISTRATION] Step 2: Updating agent prompt...')
        await updateAgentPrompt(agentId, apiKey, {
          scriptName: fileName,
          scriptContent: scriptContent,
          agentName: agentName
        })

        console.log('[TOOL_REGISTRATION] ✅ Tool registration completed')
      } catch (error) {
        console.error('[TOOL_REGISTRATION] ❌ Failed to register tool:', error)
      }
    }

    // Delay to ensure component is fully mounted
    const timeoutId = setTimeout(ensureToolRegistration, 1000)
    return () => clearTimeout(timeoutId)
  }, [isOpen, agentModality, updateAgentPrompt, fileName, scriptContent, agentName])



  // Response tab state for conversation logging
  const [conversationMessages, setConversationMessages] = useState<Array<{
    id: string;
    type: "user" | "assistant";
    content: string;
    timestamp: Date;
  }>>([])
  const [sessionStartTime, setSessionStartTime] = useState<Date | null>(null)
  const [sessionDuration, setSessionDuration] = useState(0)

  // Global recording state for Self Take functionality
  const [isRecording, setIsRecording] = useState(false)
  const [recordingError, setRecordingError] = useState<string | null>(null)
  const [recordingMode, setRecordingMode] = useState<'audio' | 'video'>('audio')
  const [recordingDuration, setRecordingDuration] = useState(0)
  const mediaRecorderRef = useRef<MediaRecorder | null>(null)
  const [currentStream, setCurrentStream] = useState<MediaStream | null>(null)
  const [hasCameraPermission, setHasCameraPermission] = useState(false)
  const recordingTimerRef = useRef<NodeJS.Timeout | null>(null)
  const [recordings, setRecordings] = useState<Array<{
    id: string;
    filename: string;
    url: string;
    timestamp: Date;
    rehearsalId: string;
    type: 'audio' | 'video';
    duration?: number;
    fileSize?: number;
  }>>([])
  const [playingRecording, setPlayingRecording] = useState<string | null>(null)
  const [audioElements, setAudioElements] = useState<Record<string, HTMLAudioElement>>({})

  // Client tools registration for ElevenLabs (no automatic UI changes)
  const clientTools = useMemo(() => {
    const tools: Record<string, (parameters: any) => any> = {};

    // Register the switch_to_script_tab client tool (response only, no UI changes)
    tools['switch_to_script_tab'] = (parameters: any) => {
      console.log("[CLIENT_TOOL] 🔧 switch_to_script_tab called with parameters:", parameters);

      const isReady = parameters?.ready === true || parameters?.user_ready === true;

      if (isReady) {
        console.log("[CLIENT_TOOL] ✅ User confirmed ready - tool acknowledged (no automatic UI changes)");

        // Return success response to agent without triggering UI changes
        const response = {
          success: true,
          message: "User readiness confirmed. Please manually navigate to the Script tab to view your script."
        };
        console.log("[CLIENT_TOOL] 📤 Returning response to agent:", response);
        return response;
      } else {
        console.warn("[CLIENT_TOOL] ⚠️ Tool called but 'ready' parameter was not true:", parameters);
        return {
          success: false,
          message: "User readiness was not confirmed in the tool parameters."
        };
      }
    };

    return tools;
  }, []);

  const conversation = useConversation({
    apiKey: process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY || '***************************************************',
    agentId: process.env.NEXT_PUBLIC_ELEVENLABS_SCRIPT_AGENT_ID || '1WU4LPk9482VXQFb80aq',
    // Add connection stability options
    maxReconnectAttempts: 3,
    reconnectInterval: 2000,
    // Register client tools
    clientTools: clientTools,
    onConnect: () => {
      console.log("Connected to ElevenLabs API successfully")
      console.log("Connection details:", {
        apiKey: (process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY || '***************************************************').substring(0, 10) + '...',
        agentId: process.env.NEXT_PUBLIC_ELEVENLABS_SCRIPT_AGENT_ID || '1WU4LPk9482VXQFb80aq'
      })
      setApiConfigStatus('valid')
      setDetailedErrorInfo(null)
      setSessionStartTime(new Date())
      setConversationMessages([]) // Clear previous messages
    },
    onDisconnect: (reason?: any) => {
      console.log("Disconnected from ElevenLabs")
      console.log("Disconnect reason:", reason)
      console.log("Connection was active for:", sessionStartTime ? (Date.now() - sessionStartTime.getTime()) / 1000 : 0, "seconds")

      setIsListening(false)
      setSessionStartTime(null)
      setSessionDuration(0)
      setConversationMessages([]) // Clear messages on disconnect

      // Only show reconnect message if this was an unexpected disconnect
      // Don't show it if the user intentionally stopped the conversation
      if (apiConfigStatus === 'valid' && reason !== 'user_initiated') {
        setVoiceErrorMessage("Connection lost. Click 'Start Rehearsing' to reconnect.")
        setApiConfigStatus('unchecked')
      } else {
        // Clear any existing error messages on intentional disconnect
        setVoiceErrorMessage("")
        setApiConfigStatus('unchecked')
      }
    },
    onError: (error: unknown) => {
      console.error("ElevenLabs API error:", error)
      console.error("Error details:", {
        type: typeof error,
        constructor: error?.constructor?.name,
        message: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      })

      // Handle CloseEvent specifically
      if (error && typeof error === 'object' && 'type' in error && error.type === 'close') {
        const closeCode = (error as any).code
        const closeReason = (error as any).reason
        const wasClean = (error as any).wasClean

        console.error("WebSocket CloseEvent detected:", {
          code: closeCode,
          reason: closeReason,
          wasClean: wasClean
        })

        // Provide specific error messages based on close codes
        let errorMessage = "Connection closed unexpectedly."
        if (closeCode === 1006) {
          errorMessage = "Connection lost abnormally. This may be a network issue."
        } else if (closeCode === 1011) {
          errorMessage = "Server error occurred. Please try again."
        } else if (closeCode === 4001) {
          errorMessage = "Authentication failed. Please check your API key."
        } else if (closeCode === 4003) {
          errorMessage = "Agent not found. Please check your agent ID."
        } else if (closeReason) {
          errorMessage = `Connection closed: ${closeReason}`
        }

        setVoiceErrorMessage(errorMessage)
        setDetailedErrorInfo(`WebSocket closed with code ${closeCode}: ${closeReason || 'No reason provided'}`)
      } else {
        const errorMessage = error instanceof Error ? error.message : String(error)
        setDetailedErrorInfo(errorMessage)
        setVoiceErrorMessage("Connection error: " + errorMessage.substring(0, 100))
      }

      setApiConfigStatus('invalid')
      if (isListening) {
        setIsListening(false)
      }
    },
    onMessage: (message) => {
      if (typeof message === "string") {
        console.log("[AUDIO] Voice input received:", message)
        // Add user message to conversation log
        const userMessage = {
          id: uuidv4(),
          type: "user" as const,
          content: message,
          timestamp: new Date()
        }
        setConversationMessages(prev => [...prev, userMessage])
      } else if (message && typeof message === "object" && 'message' in message) {
        console.log("[AUDIO] AI response received:", message.message)
        // Add assistant message to conversation log
        const assistantMessage = {
          id: uuidv4(),
          type: "assistant" as const,
          content: message.message,
          timestamp: new Date()
        }
        setConversationMessages(prev => [...prev, assistantMessage])
      }
    },
    onToolCall: (toolCall: any) => {
      console.log("[TOOL_CALL] 🔧 Function tool called by agent:", {
        toolName: toolCall?.name,
        parameters: toolCall?.parameters,
        fullToolCall: toolCall
      })

      // Handle both old and new tool names for backward compatibility
      if (toolCall?.name === 'switch_to_script_tab' || toolCall?.name === 'load_script_for_rehearsal') {
        console.log("[TOOL_CALL] 🎬 Agent called script tab switching tool!")

        // More precise check for the 'ready' or 'user_ready' parameter
        const isReady = toolCall.parameters?.ready === true || toolCall.parameters?.user_ready === true;

        if (isReady) {
          console.log("[TOOL_CALL] ✅ User confirmed ready - tool acknowledged (no automatic UI changes)")

          // Return success response without triggering UI changes
          const response = {
            success: true,
            message: "User readiness confirmed. Please manually navigate to the Script tab to view your script."
          }
          console.log("[TOOL_CALL] 📤 Returning response to agent:", response)
          return response
        } else {
          console.warn("[TOOL_CALL] ⚠️ Tool called but 'ready' parameter was not true:", toolCall.parameters)
          return {
            success: false,
            message: "User readiness was not confirmed in the tool parameters."
          }
        }
      }

      // Handle other potential tools
      console.warn("[TOOL_CALL] ❓ Unknown tool called:", toolCall?.name)
      return {
        success: false,
        message: `Unknown tool: ${toolCall?.name || 'undefined'}`
      }
    },
    onModeChange: (mode: any) => {
      console.log("[AUDIO] Mode changed to:", mode)
    },
    onStatusChange: (status: any) => {
      console.log("[AUDIO] Status changed to:", status)
    },
    // Enable audio output
    onAudioPlay: () => {
      console.log("[AUDIO] Audio playback started")
    },
    onAudioStop: () => {
      console.log("[AUDIO] Audio playback stopped")
    }
  })

  const { status: voiceStatus, isSpeaking } = conversation

  // Helper function to extract character names from script
  const extractCharacterNames = (script: string): string[] => {
    const characterPattern = /^([A-Z][A-Z\s]+):/gm
    const matches = script.match(characterPattern)
    if (!matches) return []

    const characters = matches
      .map(match => match.replace(':', '').trim())
      .filter((char, index, arr) => arr.indexOf(char) === index) // Remove duplicates
      .slice(0, 10) // Limit to first 10 characters found

    return characters
  }

  // Helper function to get voice display name from voice ID
  const getVoiceDisplayName = (voiceId: string): string | null => {
    const voice = AVAILABLE_VOICES.find(v => v.id === voiceId)
    return voice ? voice.name : null
  }

  // Helper function to update agent name to match voice
  const updateAgentNameForVoice = async (agentId: string, voiceId: string, apiKey: string) => {
    const voiceName = getVoiceDisplayName(voiceId)
    if (!voiceName) {
      throw new Error(`Unknown voice ID: ${voiceId}`)
    }

    console.log(`[VOICE_SELECT] 🏷️ Updating agent name to: "${voiceName}"`)

    // Get current agent configuration
    const currentConfig = await getAgentConfiguration(agentId, apiKey)

    // Update the agent name in the configuration
    const patchBody = {
      name: voiceName,
      conversation_config: {
        ...currentConfig.conversation_config,
        agent: {
          ...currentConfig.conversation_config?.agent,
          name: voiceName
        }
      }
    }

    // Use the direct update function from elevenlabs.ts
    const client = createElevenLabsClient(apiKey)
    const result = await client.conversationalAi.updateAgent(agentId, patchBody)

    console.log(`[VOICE_SELECT] ✅ Agent name updated successfully to: "${voiceName}"`)
    return result
  }

  // Debug function to test voice selection flow
  const debugVoiceSelection = async (testVoiceId: string) => {
    console.log("[DEBUG] Testing voice selection flow with voiceId:", testVoiceId)

    const agentId = process.env.NEXT_PUBLIC_ELEVENLABS_SCRIPT_AGENT_ID || '1WU4LPk9482VXQFb80aq'
    const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY || '***************************************************'

    console.log("[DEBUG] Configuration:", {
      agentId,
      hasApiKey: !!apiKey,
      testVoiceId,
      currentSelectedVoice: selectedVoiceId
    })

    try {
      // Test the updateAgentVoice function directly
      const result = await updateAgentVoice(agentId, testVoiceId, apiKey)
      console.log("[DEBUG] Direct updateAgentVoice call successful:", result)
      return true
    } catch (error) {
      console.error("[DEBUG] Direct updateAgentVoice call failed:", error)
      return false
    }
  }

  // Comprehensive tool system debugging function
  const debugToolSystem = async () => {
    console.log("[TOOL_DEBUG] 🔍 Starting comprehensive tool system debugging...")

    const agentId = process.env.NEXT_PUBLIC_ELEVENLABS_SCRIPT_AGENT_ID || '1WU4LPk9482VXQFb80aq'
    const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY || '***************************************************'

    try {
      // Step 1: Test API connection
      console.log("[TOOL_DEBUG] Step 1: Testing API connection...")
      const connectionResult = await testElevenLabsConnection()

      // Step 2: Get current agent configuration
      console.log("[TOOL_DEBUG] Step 2: Fetching current agent configuration...")
      const agentConfig = await getAgentConfiguration(agentId, apiKey)

      // Step 3: Analyze tool configuration
      console.log("[TOOL_DEBUG] Step 3: Analyzing tool configuration...")
      const tools = agentConfig.conversation_config?.tools || []
      const scriptTool = tools.find((tool: any) => tool.name === 'switch_to_script_tab')

      console.log("[TOOL_DEBUG] Tool analysis:", {
        totalTools: tools.length,
        hasScriptTool: !!scriptTool,
        scriptToolConfig: scriptTool,
        allTools: tools.map((t: any) => ({ name: t.name, type: t.type, description: t.description }))
      })

      // Step 4: Test client tools registration
      console.log("[TOOL_DEBUG] Step 4: Testing client tools registration...")
      const hasClientTool = 'switch_to_script_tab' in clientTools
      console.log("[TOOL_DEBUG] Client tool registration:", {
        hasClientTool,
        registeredTools: Object.keys(clientTools)
      })

      // Step 5: Configure tools if missing
      if (!scriptTool) {
        console.log("[TOOL_DEBUG] Step 5: Configuring missing tools...")
        await configureAgentClientTools(agentId, apiKey)
      }

      // Step 6: Final verification
      console.log("[TOOL_DEBUG] Step 6: Final verification...")
      const finalConfig = await getAgentConfiguration(agentId, apiKey)
      const finalTools = finalConfig.conversation_config?.tools || []
      const finalScriptTool = finalTools.find((tool: any) => tool.name === 'switch_to_script_tab')

      const result = {
        success: true,
        apiConnection: connectionResult,
        toolsConfigured: !!finalScriptTool,
        clientToolsRegistered: hasClientTool,
        totalTools: finalTools.length,
        scriptToolDetails: finalScriptTool
      }

      console.log("[TOOL_DEBUG] 🏁 Tool system debugging completed:", result)
      return result

    } catch (error) {
      console.error("[TOOL_DEBUG] ❌ Tool system debugging failed:", error)
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      }
    }
  }

  // Debugging functions removed from global scope for production
  // If needed for development, they can be called directly from the component

  // Comprehensive verification function for voice-to-agent synchronization
  const verifyVoiceAgentSync = async (voiceId: string) => {
    console.log(`[VERIFY_SYNC] 🔍 Starting comprehensive verification for voice: ${voiceId}`)

    const agentId = process.env.NEXT_PUBLIC_ELEVENLABS_SCRIPT_AGENT_ID || '1WU4LPk9482VXQFb80aq'
    const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY || '***************************************************'
    const expectedVoiceName = getVoiceDisplayName(voiceId)

    console.log(`[VERIFY_SYNC] Expected voice name: "${expectedVoiceName}"`)

    try {
      // Step 1: Verify agent configuration
      console.log(`[VERIFY_SYNC] Step 1: Fetching current agent configuration...`)
      const agentConfig = await getAgentConfiguration(agentId, apiKey)

      // Step 2: Check voice ID synchronization
      const currentTtsVoiceId = agentConfig.conversation_config?.tts?.voice_id
      const currentAgentVoiceId = agentConfig.conversation_config?.agent?.voice_id

      console.log(`[VERIFY_SYNC] Step 2: Voice ID verification:`, {
        expected_voice_id: voiceId,
        current_tts_voice_id: currentTtsVoiceId,
        current_agent_voice_id: currentAgentVoiceId,
        tts_voice_match: currentTtsVoiceId === voiceId,
        agent_voice_match: currentAgentVoiceId === voiceId
      })

      // Step 3: Check agent name synchronization
      const currentAgentName = agentConfig.name
      const currentConversationAgentName = agentConfig.conversation_config?.agent?.name

      console.log(`[VERIFY_SYNC] Step 3: Agent name verification:`, {
        expected_agent_name: expectedVoiceName,
        current_agent_name: currentAgentName,
        current_conversation_agent_name: currentConversationAgentName,
        agent_name_match: currentAgentName === expectedVoiceName,
        conversation_agent_name_match: currentConversationAgentName === expectedVoiceName
      })

      // Step 4: Check prompt content for voice name references
      const currentPrompt = agentConfig.conversation_config?.agent?.prompt?.prompt || agentConfig.prompt || ''
      const promptContainsVoiceName = currentPrompt.includes(expectedVoiceName || '')

      console.log(`[VERIFY_SYNC] Step 4: Prompt verification:`, {
        prompt_length: currentPrompt.length,
        contains_voice_name: promptContainsVoiceName,
        prompt_preview: currentPrompt.substring(0, 200) + '...'
      })

      // Step 5: Overall synchronization status
      const isFullySynced =
        currentTtsVoiceId === voiceId &&
        currentAgentName === expectedVoiceName &&
        promptContainsVoiceName

      console.log(`[VERIFY_SYNC] Step 5: Overall synchronization status:`, {
        is_fully_synced: isFullySynced,
        voice_id_synced: currentTtsVoiceId === voiceId,
        agent_name_synced: currentAgentName === expectedVoiceName,
        prompt_synced: promptContainsVoiceName
      })

      if (isFullySynced) {
        console.log(`[VERIFY_SYNC] ✅ VERIFICATION PASSED: Agent is fully synchronized with voice "${expectedVoiceName}"`)
      } else {
        console.warn(`[VERIFY_SYNC] ⚠️ VERIFICATION FAILED: Agent is NOT fully synchronized with voice "${expectedVoiceName}"`)
      }

      return {
        success: true,
        isFullySynced,
        voiceIdSynced: currentTtsVoiceId === voiceId,
        agentNameSynced: currentAgentName === expectedVoiceName,
        promptSynced: promptContainsVoiceName,
        currentConfig: {
          agentName: currentAgentName,
          ttsVoiceId: currentTtsVoiceId,
          agentVoiceId: currentAgentVoiceId,
          promptLength: currentPrompt.length
        }
      }

    } catch (error) {
      console.error(`[VERIFY_SYNC] ❌ Verification failed:`, error)
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      }
    }
  }

  // Test the complete voice selection and verification flow


  // Debug functions removed from global scope for production
  // These functions are available within the component for internal testing if needed

  // Fetch agent name on component mount and when voice changes
  const fetchAgentName = async () => {
    try {
      const agentId = process.env.NEXT_PUBLIC_ELEVENLABS_SCRIPT_AGENT_ID || '1WU4LPk9482VXQFb80aq'
      const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY || '***************************************************'

      console.log(`[AGENT_MODALITY] 🔍 Fetching agent name for agent: ${agentId}`)

      const agentConfig = await getAgentConfiguration(agentId, apiKey)
      const extractedName = extractAgentName(agentConfig)

      console.log(`[AGENT_MODALITY] ✅ Agent name fetched successfully: "${extractedName}"`)
      setAgentName(extractedName)

    } catch (error) {
      console.warn(`[AGENT_MODALITY] ⚠️ Failed to fetch agent name, using fallback:`, error)
      setAgentName('CastMate Assistant')
    }
  }

  useEffect(() => {
    fetchAgentName()
  }, [])

  // Re-fetch agent name when voice changes
  useEffect(() => {
    if (selectedVoiceId) {
      console.log(`[AGENT_MODALITY] 🔄 Voice changed to ${selectedVoiceId}, re-fetching agent name...`)
      fetchAgentName()
    }
  }, [selectedVoiceId])

  useEffect(() => {
    console.log("ElevenLabs config check:", {
      agentIdAvailable: !!process.env.NEXT_PUBLIC_ELEVENLABS_SCRIPT_AGENT_ID || '1WU4LPk9482VXQFb80aq',
      apiKeyAvailable: !!process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY || '***************************************************',
      agentId: process.env.NEXT_PUBLIC_ELEVENLABS_SCRIPT_AGENT_ID || '1WU4LPk9482VXQFb80aq',
    })
    if (isOpen) {
      setApiConfigStatus('unchecked')
      // Auto-test the connection when modal opens
      testElevenLabsConnection()

      // Ensure client tools are configured when modal opens
      const ensureClientToolsConfigured = async () => {
        try {
          const agentId = process.env.NEXT_PUBLIC_ELEVENLABS_SCRIPT_AGENT_ID || '1WU4LPk9482VXQFb80aq'
          const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY || '***************************************************'

          console.log('[MODAL_OPEN] 🔧 Ensuring client tools are configured...')
          await configureAgentClientTools(agentId, apiKey)
          console.log('[MODAL_OPEN] ✅ Client tools configuration verified')
        } catch (error) {
          console.warn('[MODAL_OPEN] ⚠️ Failed to configure client tools:', error)
        }
      }

      ensureClientToolsConfigured()
    }
  }, [isOpen])

  useEffect(() => {
    if (fileId) {
      setActiveTab(fileId)
    }
  }, [fileId])

  // Track session duration
  useEffect(() => {
    let interval: NodeJS.Timeout | null = null

    if (sessionStartTime && isListening) {
      interval = setInterval(() => {
        const now = new Date()
        const duration = Math.floor((now.getTime() - sessionStartTime.getTime()) / 1000)
        setSessionDuration(duration)
      }, 1000)
    }

    return () => {
      if (interval) {
        clearInterval(interval)
      }
    }
  }, [sessionStartTime, isListening])

  const { namespace, fileName: namespaceFileName } = useGetNamespace(userId, activeTab || null)

  useEffect(() => {
    if (namespaceFileName) {
      setFileName(namespaceFileName)
    }
  }, [namespaceFileName])

  useEffect(() => {
    if (status === StatusText.ERROR && uploadError) {
      setError(`Upload error: ${uploadError}`)
      setIsUploading(false)
      setUploadProgress(null)
    } else if (status === StatusText.UPLOADING) {
      setIsUploading(true)
      setUploadProgress(progress || 0)
      setUploadStatusText("Uploading script...")
    } else if (status === StatusText.PROCESSING) {
      setIsUploading(true)
      setUploadProgress(progress || 0)
      setUploadStatusText("Processing script...")
    } else if (status === StatusText.COMPLETED) {
      setIsUploading(false)
      setUploadProgress(null)
      fetchScriptFiles()
    }
  }, [status, progress, uploadError])

  // Audio debugging function
  const debugAudioSettings = async () => {
    console.log("[AUDIO_DEBUG] Checking browser audio capabilities...")

    try {
      // Check if audio context is available
      const AudioContext = window.AudioContext || (window as any).webkitAudioContext
      if (!AudioContext) {
        console.error("[AUDIO_DEBUG] AudioContext not supported")
        return false
      }

      // Check audio output devices
      if (navigator.mediaDevices && navigator.mediaDevices.enumerateDevices) {
        const devices = await navigator.mediaDevices.enumerateDevices()
        const audioOutputs = devices.filter(device => device.kind === 'audiooutput')
        const audioInputs = devices.filter(device => device.kind === 'audioinput')

        console.log("[AUDIO_DEBUG] Audio devices:", {
          outputs: audioOutputs.length,
          inputs: audioInputs.length,
          outputDevices: audioOutputs.map(d => ({ id: d.deviceId, label: d.label })),
          inputDevices: audioInputs.map(d => ({ id: d.deviceId, label: d.label }))
        })
      }

      // Check volume settings
      console.log("[AUDIO_DEBUG] Current audio state:", {
        isMuted: isMuted,
        conversationStatus: voiceStatus,
        isSpeaking: isSpeaking,
        isListening: isListening
      })

      return true
    } catch (error) {
      console.error("[AUDIO_DEBUG] Audio debug failed:", error)
      return false
    }
  }

  // Request camera and microphone permissions
  const requestCameraPermission = async () => {
    try {
      console.log("[VIDEO] Requesting camera and microphone permission...")
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { width: 1280, height: 720 },
        audio: true
      })
      setHasCameraPermission(true)
      setRecordingError(null)
      console.log("[VIDEO] Camera and microphone permission granted")

      // Stop the stream since we just needed permission
      stream.getTracks().forEach(track => track.stop())

    } catch (error) {
      setRecordingError("Camera access denied - please enable in browser settings")
      console.error("[VIDEO] Error accessing camera:", error)
    }
  }

  useEffect(() => {
    const requestMicPermission = async () => {
      try {
        console.log("[AUDIO] Requesting microphone permission...")
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
        setHasPermission(true)
        setVoiceErrorMessage("")
        console.log("[AUDIO] Microphone permission granted")

        // Stop the stream since we just needed permission
        stream.getTracks().forEach(track => track.stop())

        // Debug audio settings
        await debugAudioSettings()

      } catch (error) {
        setVoiceErrorMessage("Microphone access denied - please enable in browser settings")
        console.error("[AUDIO] Error accessing microphone:", error)
      }
    }

    if (activeSection === "rehearsing") {
      requestMicPermission()
      // Also request camera permission for video recording
      if (recordingMode === 'video') {
        requestCameraPermission()
      }
    }
  }, [activeSection, recordingMode])

  // Cleanup recording timer and stream on unmount
  useEffect(() => {
    return () => {
      if (recordingTimerRef.current) {
        clearInterval(recordingTimerRef.current);
      }
      if (currentStream) {
        currentStream.getTracks().forEach(track => track.stop());
      }
    };
  }, [currentStream]);

  // Test ElevenLabs API connection and tool configuration
  const testElevenLabsConnection = async () => {
    console.log("[DEBUG] Testing ElevenLabs API connection and tool configuration...")

    const agentId = process.env.NEXT_PUBLIC_ELEVENLABS_SCRIPT_AGENT_ID || '1WU4LPk9482VXQFb80aq'
    const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY || '***************************************************'

    console.log("[DEBUG] Configuration:", {
      agentId: agentId,
      apiKeyLength: apiKey?.length || 0,
      apiKeyPrefix: apiKey?.substring(0, 10) + '...',
      hasAgentId: !!agentId,
      hasApiKey: !!apiKey
    })

    try {
      const agentConfig = await getAgentConfiguration(agentId, apiKey)
      console.log("[DEBUG] ✅ ElevenLabs connection successful!")

      // Check tool configuration
      const tools = agentConfig.conversation_config?.tools || []
      const hasScriptTool = tools.some((tool: any) => tool.name === 'switch_to_script_tab')

      console.log("[DEBUG] Agent details:", {
        name: agentConfig.name,
        id: agentConfig.agent_id,
        hasConversationConfig: !!agentConfig.conversation_config,
        voiceId: agentConfig.conversation_config?.tts?.voice_id || agentConfig.conversation_config?.agent?.voice_id,
        toolsCount: tools.length,
        hasScriptTool: hasScriptTool,
        tools: tools.map((t: any) => ({ name: t.name, type: t.type }))
      })

      // If script tool is missing, try to configure it
      if (!hasScriptTool) {
        console.log("[DEBUG] 🔧 Script tool missing, attempting to configure...")
        try {
          await configureAgentClientTools(agentId, apiKey)
          console.log("[DEBUG] ✅ Client tools configured successfully")
        } catch (toolError) {
          console.warn("[DEBUG] ⚠️ Failed to configure client tools:", toolError)
        }
      }

      return true
    } catch (error) {
      console.error("[DEBUG] ❌ ElevenLabs connection failed:", error)
      return false
    }
  }

  // Debug functions removed from global scope for production

  const handleVoiceSelect = async (voiceId: string) => {
    console.log(`[VOICE_SELECT] Starting voice selection process for voiceId: ${voiceId}`)

    try {
      // Validate voiceId parameter
      if (!voiceId || typeof voiceId !== 'string') {
        throw new Error("Invalid voice ID provided")
      }

      setSelectedVoiceId(voiceId)
      setIsUpdatingVoice(true)
      setVoiceErrorMessage("") // Clear any previous errors

      // Get and validate agent ID
      const agentId = process.env.NEXT_PUBLIC_ELEVENLABS_SCRIPT_AGENT_ID || '1WU4LPk9482VXQFb80aq'
      console.log(`[VOICE_SELECT] Using agent ID: ${agentId}`)

      if (!agentId) {
        throw new Error("Missing ElevenLabs agent ID configuration")
      }

      // Get API key for the update
      const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY || '***************************************************'
      console.log(`[VOICE_SELECT] API key available: ${!!apiKey}`)

      if (!apiKey) {
        throw new Error("Missing ElevenLabs API key configuration")
      }

      console.log(`[VOICE_SELECT] Updating agent ${agentId} voice to ${voiceId}`)
      setVoiceErrorMessage("Updating voice configuration...")

      // Update the agent's voice configuration with explicit API key
      const updateResult = await updateAgentVoice(agentId, voiceId, apiKey)
      console.log(`[VOICE_SELECT] Successfully updated agent voice:`, updateResult)

      // CRITICAL FIX: Update agent name and prompt to match the selected voice
      console.log(`[VOICE_SELECT] 🔄 Updating agent name and prompt to match voice...`)
      setVoiceErrorMessage("Updating agent personality...")

      try {
        // Step 1: Update agent name to match voice
        await updateAgentNameForVoice(agentId, voiceId, apiKey)
        console.log(`[VOICE_SELECT] ✅ Agent name updated to match voice`)

        // Step 2: Update agent prompt with new personality
        const scriptContext = {
          scriptName: fileName,
          scriptContent: scriptContent,
          characterInfo: extractCharacterNames(scriptContent || ''),
          agentName: getVoiceDisplayName(voiceId) // Use the voice name as agent name
        }

        await updateAgentPrompt(agentId, apiKey, scriptContext)
        console.log(`[VOICE_SELECT] ✅ Agent prompt updated with new personality`)

        // Step 3: Update local agent name state
        const newAgentName = getVoiceDisplayName(voiceId) || 'CastMate Assistant'
        setAgentName(newAgentName)
        console.log(`[VOICE_SELECT] ✅ Local agent name state updated to: "${newAgentName}"`)

      } catch (namePromptError) {
        console.error(`[VOICE_SELECT] ⚠️ Failed to update agent name/prompt:`, namePromptError)
        // Don't fail the entire operation, just log the warning
        setVoiceErrorMessage("Voice updated, but personality update failed. Agent may still use old name.")
      }

      // Provide user feedback
      setVoiceErrorMessage("Voice updated successfully!")
      setTimeout(() => {
        setVoiceErrorMessage("")
      }, 2000)

      // If there's an active conversation, restart it with the new voice
      if (isListening && voiceStatus === 'connected') {
        console.log('[VOICE_SELECT] Restarting conversation with new voice...')
        setVoiceErrorMessage("Switching to new voice...")

        try {
          await handleEndConversation()
          // Small delay to ensure clean disconnection
          setTimeout(() => {
            handleStartConversation(0) // Reset retry count for restart
          }, 1500)
        } catch (restartError) {
          console.error('[VOICE_SELECT] Error restarting conversation:', restartError)
          setVoiceErrorMessage("Voice updated, but conversation restart failed. Please restart manually.")
        }
      }

    } catch (error) {
      console.error('[VOICE_SELECT] Error updating agent voice:', error)

      // Provide detailed error information
      const errorMessage = error instanceof Error ? error.message : String(error)
      let userFriendlyMessage = "Failed to update voice configuration"

      if (errorMessage.includes("API key") || errorMessage.includes("auth")) {
        userFriendlyMessage = "Authentication failed - please check API configuration"
      } else if (errorMessage.includes("agent") || errorMessage.includes("Agent")) {
        userFriendlyMessage = "Invalid agent configuration - please check agent ID"
      } else if (errorMessage.includes("voice") || errorMessage.includes("Voice")) {
        userFriendlyMessage = "Invalid voice selection - please try a different voice"
      } else if (errorMessage.includes("network") || errorMessage.includes("fetch")) {
        userFriendlyMessage = "Network error - please check your connection"
      }

      setVoiceErrorMessage(userFriendlyMessage)
      setDetailedErrorInfo(errorMessage)

      // Clear the error after a longer period for errors
      setTimeout(() => {
        setVoiceErrorMessage("")
        setDetailedErrorInfo(null)
      }, 5000)
    } finally {
      setIsUpdatingVoice(false)
    }
  }

  // Handle switching to script tab
  const handleSwitchToScriptTab = () => {
    console.log('[SCRIPT_NAV] User manually switched to Script tab');
    setActiveSection('script');
  };

  // Global Self Take Recording Functions
  const startSelfTakeRecording = async () => {
    if (!userId) {
      setRecordingError('Please sign in to record');
      return;
    }

    try {
      setRecordingError(null);
      setRecordingDuration(0);

      let stream: MediaStream;
      let mimeType: string;

      if (recordingMode === 'video') {
        // Request video and audio
        stream = await navigator.mediaDevices.getUserMedia({
          video: {
            width: { ideal: 1280 },
            height: { ideal: 720 },
            frameRate: { ideal: 30 }
          },
          audio: true
        });

        // Check for supported video formats
        if (MediaRecorder.isTypeSupported('video/webm;codecs=vp9,opus')) {
          mimeType = 'video/webm;codecs=vp9,opus';
        } else if (MediaRecorder.isTypeSupported('video/webm')) {
          mimeType = 'video/webm';
        } else if (MediaRecorder.isTypeSupported('video/mp4')) {
          mimeType = 'video/mp4';
        } else {
          throw new Error('No supported video format found');
        }
      } else {
        // Audio only
        stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        mimeType = 'audio/webm;codecs=opus';
      }

      setCurrentStream(stream);

      const recorder = new MediaRecorder(stream, { mimeType });
      mediaRecorderRef.current = recorder;

      const chunks: Blob[] = [];
      recorder.ondataavailable = (e) => chunks.push(e.data);
      recorder.onstop = () => {
        const blob = new Blob(chunks, { type: mimeType });
        uploadSelfTakeRecording(blob);
        stream.getTracks().forEach((track) => track.stop());
        setCurrentStream(null);
      };

      // Start recording timer
      recordingTimerRef.current = setInterval(() => {
        setRecordingDuration(prev => prev + 1);
      }, 1000);

      recorder.start();
      setIsRecording(true);

      console.log(`[SelfTake] Started ${recordingMode} recording with format: ${mimeType}`);
    } catch (error) {
      setRecordingError(`Failed to start ${recordingMode} recording. Please check your permissions.`);
      console.error('Recording error:', error);
    }
  };

  const stopSelfTakeRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);

      // Clear recording timer
      if (recordingTimerRef.current) {
        clearInterval(recordingTimerRef.current);
        recordingTimerRef.current = null;
      }
    }
  };

  const uploadSelfTakeRecording = async (blob: Blob) => {
    if (!userId) return;

    try {
      const formData = new FormData();
      const rehearsalId = `rehearsal-${Date.now()}`;
      const isVideo = recordingMode === 'video';
      const fileExtension = isVideo ? (blob.type.includes('webm') ? 'webm' : 'mp4') : 'mp3';
      const filename = `selftake-${rehearsalId}.${fileExtension}`;
      const apiEndpoint = isVideo ? "/api/selfTakeVideo" : "/api/selfTakeAudio";
      const fieldName = isVideo ? "video" : "audio";

      formData.append(fieldName, blob, filename);
      formData.append("userId", userId);
      formData.append("rehearsalId", rehearsalId);
      formData.append("scriptName", fileName || "Unknown Script");
      formData.append("duration", recordingDuration.toString());

      const response = await fetch(apiEndpoint, {
        method: "POST",
        body: formData,
      });

      if (response.ok) {
        const data = await response.json();
        const newRecording = {
          id: data.id,
          filename: data.filename,
          url: data.url,
          timestamp: new Date(data.timestamp),
          rehearsalId: rehearsalId,
          type: recordingMode,
          duration: data.duration || recordingDuration,
          fileSize: data.fileSize
        };

        setRecordings(prev => [newRecording, ...prev]);
        setRecordingError(null);
        console.log(`[SelfTake] ${recordingMode} recording uploaded successfully:`, newRecording);
      } else {
        const errorData = await response.json();
        setRecordingError(errorData.error || `Failed to save ${recordingMode} recording`);
      }
    } catch (error) {
      console.error('Upload error:', error);
      setRecordingError(`Failed to upload ${recordingMode} recording`);
    }
  };

  const toggleRecordingPlayback = (recording: any) => {
    const audioKey = recording.id;

    if (playingRecording === audioKey) {
      // Stop current playback
      if (audioElements[audioKey]) {
        audioElements[audioKey].pause();
        audioElements[audioKey].currentTime = 0;
      }
      setPlayingRecording(null);
    } else {
      // Stop any other playing audio
      Object.values(audioElements).forEach(audio => {
        audio.pause();
        audio.currentTime = 0;
      });
      setPlayingRecording(null);

      // Start new playback
      if (!audioElements[audioKey]) {
        const audio = new Audio(recording.url);
        audio.onended = () => setPlayingRecording(null);
        audio.onerror = () => {
          setRecordingError('Failed to play recording');
          setPlayingRecording(null);
        };

        setAudioElements(prev => ({ ...prev, [audioKey]: audio }));
        audio.play().then(() => setPlayingRecording(audioKey));
      } else {
        audioElements[audioKey].currentTime = 0;
        audioElements[audioKey].play().then(() => setPlayingRecording(audioKey));
      }
    }
  };

  const deleteRecording = async (recording: any) => {
    if (!userId) return;

    const recordingType = recording.type || 'audio';
    if (!confirm(`Delete ${recordingType} recording from ${recording.timestamp.toLocaleString()}?`)) {
      return;
    }

    try {
      const apiEndpoint = recordingType === 'video' ? "/api/selfTakeVideo" : "/api/selfTakeAudio";
      const response = await fetch(`${apiEndpoint}?id=${encodeURIComponent(recording.id)}&userId=${encodeURIComponent(userId)}`, {
        method: "DELETE",
      });

      if (response.ok) {
        setRecordings(prev => prev.filter(r => r.id !== recording.id));

        // Stop and clean up audio if it's playing
        if (playingRecording === recording.id) {
          if (audioElements[recording.id]) {
            audioElements[recording.id].pause();
          }
          setPlayingRecording(null);
        }

        // Remove audio element
        setAudioElements(prev => {
          const newElements = { ...prev };
          delete newElements[recording.id];
          return newElements;
        });

        console.log(`[SelfTake] ${recordingType} recording deleted successfully:`, recording.id);
      } else {
        const errorData = await response.json();
        setRecordingError(errorData.error || `Failed to delete ${recordingType} recording`);
      }
    } catch (error) {
      console.error('Delete error:', error);
      setRecordingError(`Failed to delete ${recordingType} recording`);
    }
  };

  // Load all recordings (both audio and video)
  const loadAllRecordings = async () => {
    if (!userId) return;

    try {
      // Load audio recordings
      const audioResponse = await fetch(`/api/selfTakeAudio?userId=${encodeURIComponent(userId)}`);
      const audioData = audioResponse.ok ? await audioResponse.json() : { recordings: [] };

      // Load video recordings
      const videoResponse = await fetch(`/api/selfTakeVideo?userId=${encodeURIComponent(userId)}`);
      const videoData = videoResponse.ok ? await videoResponse.json() : { recordings: [] };

      // Combine and sort recordings by timestamp
      const allRecordings = [
        ...(audioData.recordings || []).map((r: any) => ({ ...r, type: 'audio' })),
        ...(videoData.recordings || []).map((r: any) => ({ ...r, type: 'video' }))
      ].sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

      setRecordings(allRecordings);
      console.log('[SelfTake] Loaded recordings:', {
        audio: audioData.recordings?.length || 0,
        video: videoData.recordings?.length || 0,
        total: allRecordings.length
      });
    } catch (error) {
      console.error('Error loading recordings:', error);
      setRecordingError('Failed to load recordings');
    }
  };

  const handleStartConversation = async (retryCount = 0) => {
    console.log("[CONVERSATION_START] Initiating conversation start process")

    try {
      setVoiceErrorMessage("")
      setDetailedErrorInfo(null)

      // Validate user authentication
      if (!session?.user?.email) {
        throw new Error("You must be signed in to use voice features")
      }

      // Validate voice selection
      if (!selectedVoiceId) {
        setVoiceErrorMessage("Please select a voice before starting the conversation")
        return
      }

      // Validate agent configuration
      const agentId = process.env.NEXT_PUBLIC_ELEVENLABS_SCRIPT_AGENT_ID || '1WU4LPk9482VXQFb80aq'
      if (!agentId) {
        throw new Error("Missing ElevenLabs agent ID configuration")
      }

      // Validate API key
      const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY || '***************************************************'
      if (!apiKey) {
        throw new Error("Missing ElevenLabs API key configuration")
      }

      // Enhanced validation - check if agent actually exists
      console.log("[CONVERSATION_START] Validating agent existence and API key...")
      try {
        const testAgentConfig = await getAgentConfiguration(agentId, apiKey)
        console.log("[CONVERSATION_START] ✅ Agent validation successful - agent exists and API key is valid")
        console.log("[CONVERSATION_START] Agent name:", testAgentConfig.name || 'Unknown')
      } catch (validationError) {
        console.error("[CONVERSATION_START] ❌ Agent validation failed:", validationError)
        const errorMsg = validationError instanceof Error ? validationError.message : String(validationError)

        if (errorMsg.includes('401') || errorMsg.includes('unauthorized') || errorMsg.includes('API key')) {
          throw new Error("Invalid API key - please check your ElevenLabs API key configuration")
        } else if (errorMsg.includes('404') || errorMsg.includes('not found')) {
          throw new Error(`Agent ID ${agentId} not found - please check your agent configuration`)
        } else {
          throw new Error(`Agent validation failed: ${errorMsg}`)
        }
      }

      console.log("[CONVERSATION_START] Configuration validated:", {
        agentId: agentId,
        voiceId: selectedVoiceId,
        scriptId: activeTab,
        scriptName: fileName,
        hasApiKey: !!apiKey,
        agentModality: agentModality
      })

      setApiConfigStatus('connecting')
      setIsListening(true)

      // CRITICAL: Apply the selected modality to the agent BEFORE starting conversation
      console.log(`[AGENT_MODALITY] 🔄 Applying ${agentModality} modality to agent before conversation start...`)
      setVoiceErrorMessage(`Configuring agent for ${agentModality} mode...`)

      try {
        // Prepare script context for prompt generation
        const scriptContext = {
          scriptName: fileName,
          scriptContent: scriptContent,
          characterInfo: extractCharacterNames(scriptContent || ''),
          agentName: agentName || getVoiceDisplayName(selectedVoiceId) || 'CastMate Assistant'
        }

        console.log(`[AGENT_MODALITY] 📝 Updating agent prompt with ${agentModality} modality...`)
        console.log(`[AGENT_MODALITY] Script context:`, {
          scriptName: scriptContext.scriptName || 'No script',
          hasScriptContent: !!scriptContext.scriptContent,
          scriptContentLength: scriptContext.scriptContent?.length || 0,
          characterCount: scriptContext.characterInfo?.length || 0,
          agentName: scriptContext.agentName
        })

        // Apply the modality to the agent
        await updateAgentPrompt(agentId, apiKey, scriptContext)
        console.log(`[AGENT_MODALITY] ✅ Agent prompt updated successfully with ${agentModality} modality`)

      } catch (modalityError) {
        console.error(`[AGENT_MODALITY] ❌ Failed to apply ${agentModality} modality:`, modalityError)
        setVoiceErrorMessage(`Failed to configure ${agentModality} mode. Using default settings...`)
        // Continue with conversation start even if modality update fails
      }

      setVoiceErrorMessage("Starting conversation...")

      // Simple approach like legacy implementation - just start the conversation
      console.log("[CONVERSATION_START] Starting conversation with simplified approach...")

      // Prepare conversation parameters (simplified like legacy implementation)
      const requestParams: any = {}
      if (activeTab) {
        requestParams.scriptId = activeTab
        requestParams.scriptName = fileName || "Unknown script"
      }

      console.log("[CONVERSATION_START] Attempting to start conversation with agent ID:", agentId)

      const conversationId = await conversation.startSession(requestParams)

      console.log("[CONVERSATION_START] ElevenLabs conversation started successfully:", conversationId)
      console.log(`[AGENT_MODALITY] 🎉 Conversation active with ${agentModality} modality`);
      console.log(`[AGENT_MODALITY] Final conversation configuration:`, {
        conversationId: conversationId,
        agentModality: agentModality,
        scriptName: fileName || 'No script',
        voiceId: selectedVoiceId,
        agentId: agentId
      });

      setApiConfigStatus('valid')
      setVoiceErrorMessage("Connected! You can now speak.")

      // Check connection stability after a brief delay
      setTimeout(() => {
        if (conversation.status === 'connected') {
          console.log(`[CONNECTION_STABLE] Connection stable after 2 seconds`)
        } else {
          console.warn(`[CONNECTION_UNSTABLE] Connection status changed to: ${conversation.status}`)
          // Only show connection lost message if truly disconnected AND not currently speaking
          // This prevents false "connection lost" messages when the agent is actively speaking
          if (conversation.status === 'disconnected' && !isSpeaking) {
            console.log(`[CONNECTION_UNSTABLE] Showing connection lost message - agent is not speaking`)
            setVoiceErrorMessage("Connection lost. Click 'Start Rehearsing' to reconnect.")
          } else if (conversation.status === 'disconnected' && isSpeaking) {
            console.log(`[CONNECTION_UNSTABLE] Agent is speaking - suppressing connection lost message`)
          }
        }
      }, 2000)

      // Clear success message after a few seconds
      setTimeout(() => {
        if (apiConfigStatus === 'valid') {
          setVoiceErrorMessage("")
        }
      }, 3000)

    } catch (error) {
      console.error("[CONVERSATION_START] Error starting conversation:", error)

      let errorMessage = "Failed to start conversation"
      let detailedMessage = error instanceof Error ? error.message : String(error)

      // Check if this is an authorization error and we haven't retried too many times
      if ((detailedMessage.includes("Could not authorize the conversation") || detailedMessage.includes("authorize")) && retryCount < 2) {
        console.log(`[CONVERSATION_START] Authorization failed, retrying in 3 seconds... (attempt ${retryCount + 1}/3)`)
        setVoiceErrorMessage(`Authorization failed, retrying in 3 seconds... (attempt ${retryCount + 1}/3)`)

        setTimeout(() => {
          handleStartConversation(retryCount + 1)
        }, 3000)
        return
      }

      // Provide specific error messages based on error type
      if (detailedMessage.includes("agentId") || detailedMessage.includes("agent")) {
        errorMessage = "Invalid agent ID configuration"
      } else if (detailedMessage.includes("auth") || detailedMessage.includes("API key") || detailedMessage.includes("key") || detailedMessage.includes("authorize")) {
        errorMessage = "API authentication failed - please wait a moment and try again"
      } else if (detailedMessage.includes("Could not authorize the conversation")) {
        errorMessage = "Authorization failed after multiple attempts - agent configuration may still be processing. Please wait 30 seconds and try again."
      } else if (detailedMessage.includes("network") || detailedMessage.includes("connect") || detailedMessage.includes("fetch")) {
        errorMessage = "Network connection failed - please check your internet connection"
      } else if (detailedMessage.includes("sign in") || detailedMessage.includes("authentication")) {
        errorMessage = "Authentication required - please sign in"
      } else if (detailedMessage.includes("voice") || detailedMessage.includes("Voice")) {
        errorMessage = "Voice configuration error - please try selecting a different voice"
      } else if (detailedMessage.includes("microphone") || detailedMessage.includes("permission")) {
        errorMessage = "Microphone permission required - please enable microphone access"
      }

      setVoiceErrorMessage(errorMessage)
      setDetailedErrorInfo(detailedMessage)
      setIsListening(false)
      setApiConfigStatus('invalid')
    }
  }

  const handleEndConversation = async () => {
    try {
      setIsListening(false)
      await conversation.endSession()
    } catch (error) {
      console.error("Error ending conversation:", error)
      setVoiceErrorMessage("Failed to end conversation")
      setDetailedErrorInfo(error instanceof Error ? error.message : String(error))
    }
  }

  const toggleMute = async () => {
    try {
      console.log("[AUDIO] Toggling mute. Current state:", { isMuted, voiceStatus })

      // Check if conversation is active
      if (voiceStatus !== 'connected') {
        console.warn("[AUDIO] Cannot change volume - conversation not connected")
        setVoiceErrorMessage("Start a conversation first to control volume")
        return
      }

      const newVolume = isMuted ? 1 : 0
      console.log("[AUDIO] Setting volume to:", newVolume)

      // Try to set volume using the conversation object
      if (conversation.setVolume) {
        conversation.setVolume({ volume: newVolume })
        setIsMuted(!isMuted)
        console.log("[AUDIO] Volume changed successfully to:", newVolume)
      } else {
        console.warn("[AUDIO] setVolume method not available on conversation object")
        setVoiceErrorMessage("Volume control not available")
      }
    } catch (error) {
      console.error("[AUDIO] Error changing volume:", error)
      setVoiceErrorMessage("Failed to change volume")
      setDetailedErrorInfo(error instanceof Error ? error.message : String(error))
    }
  }

  const fetchMostRecentFile = async (): Promise<{id: string, namespace: string, name: string} | null> => {
    if (!userId) return null
    try {
      const filesRef = collection(db, `users/${userId}/files`)
      const q = query(filesRef, orderBy("createdAt", "desc"), limit(1))
      const querySnapshot = await getDocs(q)
      if (!querySnapshot.empty) {
        const fileDoc = querySnapshot.docs[0]
        const fileData = fileDoc.data()
        return {
          id: fileDoc.id,
          namespace: fileData.namespace || fileDoc.id,
          name: fileData.name || "Untitled Document"
        }
      }
      return null
    } catch (err) {
      console.error("Error fetching most recent file:", err)
      return null
    }
  }

  const fetchScriptFiles = async () => {
    if (sessionStatus === "loading") {
      return
    }
    if (!session?.user?.email) {
      setError("Please sign in to access your scripts.")
      setLoading(false)
      return
    }

    try {
      const db = getFirestore()
      const filesRef = collection(db, `users/${session.user.email}/files`)
      const q = query(
        filesRef,
        where("category", "==", "SceneMate"),
        orderBy("name", "asc")
      )
      const querySnapshot = await getDocs(q)

      const files: ScriptFile[] = []
      querySnapshot.forEach((doc) => {
        const fileData = doc.data()
        files.push({
          id: doc.id,
          name: fileData.name || "Untitled Script",
          namespace: fileData.namespace || doc.id
        })
      })
      files.sort((a, b) => a.name.localeCompare(b.name))
      setScriptFiles(files)
      if (!activeTab && files.length > 0) {
        setActiveTab(files[0].id)
      }
      setLoading(false)
    } catch (err) {
      console.error("Error fetching script files:", err)
      setError(`Failed to load scripts: ${err instanceof Error ? err.message : "Unknown error"}`)
      setLoading(false)
    }
  }

  useEffect(() => {
    if (sessionStatus === "authenticated") {
      fetchScriptFiles()
    }
  }, [sessionStatus, userId, activeTab])

  const fetchFileDocumentId = async (namespace: string) => {
    if (!userId) return
    try {
      const filesRef = collection(db, `users/${userId}/files`)
      const q = query(filesRef, where("namespace", "==", namespace), limit(1))
      const querySnapshot = await getDocs(q)
      if (!querySnapshot.empty) {
        const fileDoc = querySnapshot.docs[0]
        setFileDocumentId(fileDoc.id)
        const fileData = fileDoc.data()
        if (fileData.name) {
          setFileName(fileData.name)
        }
      }
    } catch (err) {
      console.error("Error fetching file document ID:", err)
    }
  }

  const fetchScriptContent = async () => {
    if (!activeTab || !userId) {
      setError("No script selected or user not authenticated")
      return
    }

    setIsScriptLoading(true)
    setError(null)

    try {
      // Get file document to retrieve namespace
      const fileDocRef = doc(db, `users/${userId}/files/${activeTab}`)
      const fileDocSnap = await getDoc(fileDocRef)

      if (!fileDocSnap.exists()) {
        setError("Script file not found")
        setIsScriptLoading(false)
        return
      }

      const fileData = fileDocSnap.data()
      const fileNamespace = fileData.namespace || activeTab

      const chunksRef = collection(db, `users/${userId}/byteStoreCollection`)
      const q = query(chunksRef, where("metadata.doc_id", "==", fileNamespace))
      const querySnapshot = await getDocs(q)
      const chunks = querySnapshot.docs.map((d) => d.data())

      if (chunks.length === 0) {
        setError("No content found for this script")
        setIsScriptLoading(false)
        return
      }

      // Sort chunks by position or page_number
      if ("position" in chunks[0]) {
        chunks.sort((a, b) => (a.position || 0) - (b.position || 0))
      } else if ("metadata" in chunks[0] && "page_number" in chunks[0].metadata) {
        chunks.sort((a, b) => (a.metadata.page_number || 0) - (b.metadata.page_number || 0))
      }

      // Determine content field and assemble
      const contentField = "pageContent" in chunks[0] ? "pageContent" : "content"
      const content = chunks.map((chunk) => chunk[contentField] || "").join("\n")
      setScriptContent(content)
      setIsScriptReady(true)
    } catch (err) {
      console.error("Error fetching script content:", err)
      setError("Failed to load script content")
      setIsScriptLoading(false)
    } finally {
      setIsScriptLoading(false)
    }
  }

  const createNewChat = async (fileNamespace?: string, fileDocId?: string) => {
    if (!userId) {
      setError("Please sign in to create a new chat.")
      return null
    }

    try {
      let firstMessageText = "New Rehearsal"
      const chatsRef = collection(db, `users/${userId}/chats`)
      let actualFileDocId: string
      let actualNamespace: string

      if (fileNamespace && fileDocId) {
        actualNamespace = fileNamespace
        actualFileDocId = fileDocId
      } else if (fileDocId) {
        actualNamespace = fileDocId
        actualFileDocId = fileDocId
      } else if (fileNamespace) {
        actualNamespace = fileNamespace
        actualFileDocId = fileNamespace
      } else {
        const recentFile = await fetchMostRecentFile()
        if (recentFile) {
          actualNamespace = recentFile.namespace
          actualFileDocId = recentFile.id
          setFileName(recentFile.name)
        } else {
          setError("Please upload or select a file before creating a chat.")
          return null
        }
      }

      const chatData = {
        createdAt: serverTimestamp(),
        userId: userId,
        firstMessage: firstMessageText,
        lastUpdated: serverTimestamp(),
        fileNamespace: actualNamespace,
        fileDocumentId: actualFileDocId
      }

      const docRef = await addDoc(chatsRef, chatData)
      const newChatId = docRef.id
      setChatId(newChatId)
      setChatMessages([])
      setSelectedFileNamespace(actualNamespace)
      setFileDocumentId(actualFileDocId)
      return newChatId
    } catch (err) {
      if (isMounted.current) {
        setError(
          "Failed to create new chat: " +
            (err instanceof Error ? err.message : "Unknown error")
        )
      }
      return null
    }
  }

  const handleFileUpload = useCallback(
    async (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0]
      if (!file || !userId) {
        setError("No file selected or user not authenticated.")
        return
      }
      try {
        const docId = uuidv4()
        await handleUpload(file, null, userId, docId)
        const newChatId = await createNewChat(docId, docId)

        if (newChatId) {
          setSelectedFileNamespace(docId)
          setFileDocumentId(docId)
          setFileName(file.name)
          setChatId(newChatId)
          const messagesRef = collection(db, `users/${userId}/chats/${newChatId}/messages`)
          const welcomeMessageData = {
            role: "assistant",
            text: "File processed successfully! How can I assist with your script?",
            createdAt: serverTimestamp(),
            fileDocumentId: docId
          }
          const welcomeDocRef = await addDoc(messagesRef, welcomeMessageData)
          const initialMessage: ChatMessage = {
            id: welcomeDocRef.id,
            role: "assistant",
            content: welcomeMessageData.text,
            timestamp: new Date().toISOString(),
            fileDocumentId: docId
          }
          setChatMessages([initialMessage])
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "Upload failed")
      }
    },
    [userId, handleUpload, createNewChat]
  )

  const handleUploadClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click()
    }
  }

  const handleScriptDeleted = useCallback(async () => {
    console.log("Script deleted, updating UI state...");

    // Store the current active tab before refreshing
    const deletedScriptId = activeTab;

    // 1. Clear script selection state immediately
    setActiveTab(null);
    setFileName(null);

    // 2. Reset all script-related state variables
    setScriptContent("");
    setIsScriptLoading(false);
    setIsScriptReady(false);
    setIsFormatting(false);
    setFormattedMarkdown("");

    // 3. Clear any error states
    setError(null);

    // 4. Refresh the script files list to remove deleted script
    try {
      const db = getFirestore();
      const filesRef = collection(db, `users/${session?.user?.email}/files`);
      const q = query(
        filesRef,
        where("category", "==", "SceneMate"),
        orderBy("name", "asc")
      );
      const querySnapshot = await getDocs(q);

      const files: ScriptFile[] = [];
      querySnapshot.forEach((doc) => {
        const fileData = doc.data();
        files.push({
          id: doc.id,
          name: fileData.name || "Untitled Script",
          namespace: fileData.namespace || doc.id
        });
      });

      files.sort((a, b) => a.name.localeCompare(b.name));
      setScriptFiles(files);

      // 5. Handle script selection after refresh
      if (files.length > 0) {
        // Find a script to select (avoid the deleted one)
        const remainingScripts = files.filter(file => file.id !== deletedScriptId);
        if (remainingScripts.length > 0) {
          // Leave selection empty - let user choose
          console.log(`${remainingScripts.length} scripts remaining after deletion`);
          setActiveTab(null);
        } else {
          // All scripts were deleted, leave selection empty
          console.log("No scripts remaining after deletion");
          setActiveTab(null);
        }
      } else {
        // No scripts left, leave selection empty
        console.log("No scripts available after deletion");
        setActiveTab(null);
      }

      setLoading(false);
    } catch (err) {
      console.error("Error refreshing script files after deletion:", err);
      setError(`Failed to refresh scripts: ${err instanceof Error ? err.message : "Unknown error"}`);
      setLoading(false);
    }
  }, [activeTab, session?.user?.email]);

  useEffect(() => {
    if (activeSection === "script" && activeTab) {
      fetchScriptContent()
    }
  }, [activeSection, activeTab])

  // Placeholder for AI formatting (assuming scriptFormatter and markdownFormatterTool exist)
  useEffect(() => {
    if (isScriptReady && scriptContent && !isFormatting) {
      const formatScriptContent = async () => {
        setIsFormatting(true)
        try {
          // Simulate AI formatting (replace with actual implementation if tools are available)
          const formattedScript = {
            metadata: { title: fileName || "Untitled", author: "", characters: [], summary: "" },
            lines: scriptContent.split("\n").map((line, idx) => ({
              lineNumber: idx + 1,
              text: line,
            })),
          }
          setFormattedMarkdown(scriptContent)
        } catch (err) {
          console.error("Error formatting script:", err)
          setFormattedMarkdown(scriptContent)
        } finally {
          setIsFormatting(false)
        }
      }
      formatScriptContent()
    }
  }, [isScriptReady, scriptContent, isFormatting, fileName])

  useEffect(() => {
    return () => {
      isMounted.current = false
    }
  }, [])

  // Cleanup recording when modal closes
  useEffect(() => {
    return () => {
      if (isRecording) {
        stopSelfTakeRecording()
      }
    }
  }, [isOpen])

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 dark:bg-black/80 backdrop-blur-sm p-2 sm:p-4 md:p-0">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="relative w-full max-w-full sm:max-w-3xl md:max-w-4xl lg:max-w-6xl h-[90vh] sm:h-[85vh] bg-white dark:bg-gray-900 rounded-2xl shadow-2xl overflow-hidden border border-gray-200 dark:border-gray-700 transition-colors duration-300"
      >
        <div className="absolute inset-0 bg-gradient-to-t from-gray-50/30 dark:from-gray-800/10 to-transparent pointer-events-none" />

        <div className="absolute top-4 left-4 flex flex-col space-y-1">
          {sessionStatus === "loading" && (
            <div className="bg-yellow-500/20 text-yellow-300 text-xs px-2 py-1 rounded-md flex items-center">
              <Loader className="w-3 h-3 mr-1 animate-spin" />
              Loading session...
            </div>
          )}
          {sessionStatus === "unauthenticated" && (
            <div className="bg-red-500/20 text-red-300 text-xs px-2 py-1 rounded-md flex items-center">
              <Info className="w-3 h-3 mr-1" />
              Not authenticated
            </div>
          )}
          {apiConfigStatus === 'connecting' && (
            <div className="bg-yellow-500/20 text-yellow-300 text-xs px-2 py-1 rounded-md flex items-center">
              <Loader className="w-3 h-3 mr-1 animate-spin" />
              Connecting to voice API...
            </div>
          )}
          {apiConfigStatus === 'invalid' && (
            <div className="bg-red-500/20 text-red-300 text-xs px-2 py-1 rounded-md flex items-center">
              <Info className="w-3 h-3 mr-1" />
              ElevenLabs API issue
            </div>
          )}
        </div>



        <div className="flex flex-col md:flex-row h-full">
          <AnimatePresence>
            {(isMobileSidebarOpen || isOpen) && (
              <motion.div
                key="sidebar-motion"
                initial={{ x: -300, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                exit={{ x: -300, opacity: 0 }}
                transition={{ duration: 0.2 }}
              >
                <SideBar
                  activeTab={activeTab}
                  setActiveTab={setActiveTab}
                  scriptFiles={scriptFiles}
                  loading={loading}
                  error={error}
                  setError={setError}
                  isUploading={isUploading}
                  uploadProgress={uploadProgress}
                  uploadStatusText={uploadStatusText}
                  handleUploadClick={handleUploadClick}
                  handleFileUpload={handleFileUpload}
                  fileInputRef={fileInputRef}
                  sessionStatus={sessionStatus}
                  session={session}
                  onClose={onClose}
                  isMobileSidebarOpen={isMobileSidebarOpen}
                  setIsMobileSidebarOpen={setIsMobileSidebarOpen}
                />
              </motion.div>
            )}
          </AnimatePresence>

          <div className="flex-1 overflow-hidden backdrop-blur-sm">
            <div className="h-full flex flex-col">
              {/* Recording Status Bar */}
              {isRecording && (
                <div className="bg-red-600/90 backdrop-blur-sm px-4 py-2 flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-red-300 rounded-full animate-pulse"></div>
                      <span className="text-white font-medium text-sm">Recording Self Take</span>
                    </div>
                    <span className="text-red-100 text-xs">
                      Recording will continue while you navigate between tabs
                    </span>
                  </div>
                  <button
                    onClick={stopSelfTakeRecording}
                    className="bg-white/20 hover:bg-white/30 text-white px-3 py-1 rounded text-sm font-medium transition-colors"
                  >
                    Stop Recording
                  </button>
                </div>
              )}

              <div className="border-b border-gray-200 dark:border-gray-700 px-4 sm:px-6 py-3 sm:py-4 bg-gray-50 dark:bg-gray-800/50 transition-colors duration-300">
                <div className="flex flex-wrap gap-4 sm:space-x-6 pt-3 justify-between items-center">
                  <div className="flex flex-wrap gap-4 sm:space-x-6">
                    {[
                      { name: "rehearsing", label: "Connecting", icon: <Mic className="w-3 h-3 sm:w-4 sm:h-4 mr-1" /> },
                      { name: "chat", label: "Tutor", icon: <MessageSquare className="w-3 h-3 sm:w-4 sm:h-4 mr-1" /> },
                      { name: "script", label: "Script", icon: <Book className="w-3 h-3 sm:w-4 sm:h-4 mr-1" /> },
                      {
                        name: "response",
                        label: "Self Take",
                        icon: <MessageCircle className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />,
                        hasRecordingIndicator: isRecording && activeSection !== "response"
                      },
                      { name: "details", label: "Details", icon: <FileText className="w-3 h-3 sm:w-4 sm:h-4 mr-1" /> },
                    ].map((section) => (
                      <button
                        key={section.name}
                        onClick={() => setActiveSection(section.name)}
                        className={`relative text-xs sm:text-sm font-medium px-3 py-2 rounded-md transition-all duration-200 flex items-center border-b-2 ${
                          activeSection === section.name
                            ? "border-blue-500 dark:border-blue-400 text-blue-600 dark:text-blue-400 bg-blue-50/50 dark:bg-blue-500/10"
                            : "border-transparent text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-300 hover:border-blue-300 dark:hover:border-blue-500 hover:bg-gray-50 dark:hover:bg-white/5"
                        } ${
                          ['rehearsing', 'chat', 'response', 'details'].includes(section.name) ? 'courier-font' : ''
                        }`}
                      >
                        {section.icon}
                        {section.label}
                        {section.hasRecordingIndicator && (
                          <div className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                        )}
                      </button>
                    ))}
                  </div>

                  <div className="flex items-center space-x-2">
                    {/* Theme Toggle */}
                    <CompactThemeToggle />

                    {/* Desktop Exit Button */}
                    <button
                    onClick={(e) => {
                      e.preventDefault()
                      e.stopPropagation()
                      onClose()
                    }}
                    className="hidden md:flex p-1.5 rounded-full bg-gray-100 dark:bg-gray-600/80 hover:bg-gray-200 dark:hover:bg-gray-500 text-gray-600 dark:text-white transition-all duration-300 shadow-sm items-center justify-center border border-gray-200 dark:border-transparent hover:border-gray-300 dark:hover:border-gray-400/50"
                    aria-label="Close"
                    type="button"
                    title="Close Script Reader"
                  >
                    <X className="w-3.5 h-3.5" />
                  </button>
                  </div>
                </div>
              </div>

              <div className="flex-1 overflow-y-auto p-4 sm:p-6 scrollbar-thin scrollbar-track-gray-100 dark:scrollbar-track-gray-800 scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 hover:scrollbar-thumb-gray-400 dark:hover:scrollbar-thumb-gray-500">
                <AnimatePresence mode="wait">
                  <motion.div
                    key={`${activeTab}-${activeSection}`}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.2 }}
                    className="space-y-6 h-full"
                  >
                    {activeSection === "rehearsing" && (
                      <Rehearsals
                        apiConfigStatus={apiConfigStatus}
                        detailedErrorInfo={detailedErrorInfo}
                        isListening={isListening}
                        voiceStatus={voiceStatus}
                        isMuted={isMuted}
                        isSpeaking={isSpeaking}
                        hasPermission={hasPermission}
                        voiceErrorMessage={voiceErrorMessage}
                        toggleMute={toggleMute}
                        handleEndConversation={handleEndConversation}
                        handleStartConversation={handleStartConversation}
                        setVoiceErrorMessage={setVoiceErrorMessage}
                        selectedScriptName={scriptFiles.find(file => file.id === activeTab)?.name}
                        selectedVoiceId={selectedVoiceId}
                        onVoiceSelect={handleVoiceSelect}
                        isUpdatingVoice={isUpdatingVoice}
                        agentModality={agentModality}
                        onAgentModalityChange={setAgentModality}
                        onSwitchToScriptTab={handleSwitchToScriptTab}
                        conversationMessages={conversationMessages}
                      />
                    )}

                    {activeSection === "chat" && <ChatTab chatId={activeTab ?? ''} namespace={namespace} />}

                    {activeSection === "script" && (
                      <ScriptTab
                        scriptContent={scriptContent}
                        isScriptLoading={isScriptLoading || isFormatting}
                        isScriptReady={isScriptReady && !isFormatting}
                        scriptName={fileName}
                        scriptId={activeTab}
                        isListening={isListening}
                        isMuted={isMuted}
                        toggleMute={toggleMute}
                        handleEndConversation={handleEndConversation}
                        handleStartConversation={handleStartConversation}
                        onScriptDeleted={handleScriptDeleted}
                        apiConfigStatus={apiConfigStatus}
                        hasPermission={hasPermission}
                        voiceStatus={voiceStatus}
                        selectedVoiceId={selectedVoiceId}
                      />
                    )}

                    {activeSection === "response" && (
                      <ResponseTab
                        isConnected={voiceStatus === 'connected'}
                        conversationMessages={conversationMessages}
                        onClearMessages={() => setConversationMessages([])}
                        scriptName={fileName}
                        voiceId={selectedVoiceId}
                        sessionDuration={sessionDuration}
                        voiceStatus={voiceStatus}
                        isRecording={isRecording}
                        recordingError={recordingError}
                        recordings={recordings}
                        playingRecording={playingRecording}
                        recordingMode={recordingMode}
                        recordingDuration={recordingDuration}
                        currentStream={currentStream}
                        hasCameraPermission={hasCameraPermission}
                        onStartRecording={startSelfTakeRecording}
                        onStopRecording={stopSelfTakeRecording}
                        onTogglePlayback={toggleRecordingPlayback}
                        onDeleteRecording={deleteRecording}
                        onRecordingModeChange={setRecordingMode}
                        onLoadRecordings={loadAllRecordings}
                        onRequestCameraPermission={requestCameraPermission}
                      />
                    )}

                    {activeSection === "details" && (
                      <FileDetails
                        activeTab={activeTab}
                        fileName={fileName}
                        namespace={namespace}
                        apiConfigStatus={apiConfigStatus}
                        sessionStatus={sessionStatus}
                        detailedErrorInfo={detailedErrorInfo}
                      />
                    )}
                  </motion.div>
                </AnimatePresence>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  )
}

export { Readermodal }